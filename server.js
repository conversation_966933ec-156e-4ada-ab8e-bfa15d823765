const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const net = require('net');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = socketIo(server);

// 存储扫描进度的文件
const PROGRESS_FILE = 'scan_progress.json';
const RESULTS_FILE = 'scan_results.json';
const CHAT_TEST_RESULTS_FILE = 'chat_test_results.json';

// 存储聊天测试结果
let chatTestResults = [];

// 日本IP段范围
const JP_IP_RANGES = [
    { start: '********', end: '**************' },
    { start: '*********', end: '**************' },
    { start: '*********', end: '**************' },
    { start: '********', end: '**************' },
    { start: '60.0.0.0', end: '**************' },
    { start: '*********', end: '**************' },
    { start: '*********', end: '***************' },
    { start: '***********', end: '***************' }
];

// IP地址转换函数
function ip2long(ip) {
    return ip.split('.')
        .reduce((int, oct) => (int << 8) + parseInt(oct, 10), 0) >>> 0;
}

function long2ip(long) {
    return [
        (long >>> 24) & 255,
        (long >>> 16) & 255,
        (long >>> 8) & 255,
        long & 255
    ].join('.');
}

// IP解析函数
function parseIPInput(input) {
    const ips = new Set();
    const lines = input.split('\n');
    
    lines.forEach(line => {
        line = line.trim();
        if (line.includes('-')) {
            // IP范围格式: ***********-*************
            const [start, end] = line.split('-').map(ip => ip.trim());
            const startLong = ip2long(start);
            const endLong = ip2long(end);
            for (let ipLong = startLong; ipLong <= endLong; ipLong++) {
                ips.add(long2ip(ipLong));
            }
        } else if (line) {
            // 单个IP
            ips.add(line);
        }
    });
    return Array.from(ips);
}

// 加载进度
function loadProgress() {
    try {
        if (fs.existsSync(PROGRESS_FILE)) {
            return JSON.parse(fs.readFileSync(PROGRESS_FILE));
        }
    } catch (err) {
        console.error('Error loading progress:', err);
    }
    return { lastScannedIP: null };
}

// 加载聊天测试结果
function loadChatTestResults() {
    try {
        if (fs.existsSync(CHAT_TEST_RESULTS_FILE)) {
            chatTestResults = JSON.parse(fs.readFileSync(CHAT_TEST_RESULTS_FILE));
        }
    } catch (err) {
        console.error('Error loading chat test results:', err);
        chatTestResults = [];
    }
}

// 保存进度
function saveProgress(ip) {
    fs.writeFileSync(PROGRESS_FILE, JSON.stringify({ lastScannedIP: ip }));
}

// 保存聊天测试结果
function saveChatTestResult(result) {
    chatTestResults.push(result);
    // 只保留最近1000条记录，避免文件过大
    if (chatTestResults.length > 1000) {
        chatTestResults = chatTestResults.slice(-1000);
    }
    fs.writeFileSync(CHAT_TEST_RESULTS_FILE, JSON.stringify(chatTestResults, null, 2));
}

// 保存结果
function saveResult(ip, data) {
    let results = [];
    try {
        if (fs.existsSync(RESULTS_FILE)) {
            results = JSON.parse(fs.readFileSync(RESULTS_FILE));
        }
    } catch (err) {
        console.error('Error loading results:', err);
    }
    
    results.push({ ip, data, timestamp: new Date().toISOString() });
    fs.writeFileSync(RESULTS_FILE, JSON.stringify(results, null, 2));
}

// 端口扫描函数
async function scanPort(ip, port, retryCount = 0) {
    return new Promise((resolve) => {
        const socket = new net.Socket();
        let resolved = false;
        
        socket.setTimeout(5000);

        const cleanup = () => {
            if (!resolved) {
                resolved = true;
                try {
                    socket.destroy();
                } catch (e) {}
            }
        };

        socket.on('connect', () => {
            cleanup();
            resolve({ success: true });
        });

        socket.on('timeout', () => {
            cleanup();
            resolve({ success: false, canRetry: retryCount < 2 });
        });

        socket.on('error', (err) => {
            cleanup();
            if (err.code === 'ECONNREFUSED') {
                resolve({ success: false, canRetry: false });
            } else {
                resolve({ success: false, canRetry: retryCount < 2 });
            }
        });

        try {
            socket.connect(port, ip);
        } catch (err) {
            cleanup();
            resolve({ success: false, canRetry: retryCount < 2 });
        }
    });
}

// 检查Ollama服务并获取模型列表
async function checkOllama(ip, retryCount = 0) {
    try {
        const response = await axios.get(`http://${ip}:11434/`, {
            timeout: 5000,
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
            }
        });
        
        if (response.data && response.data.includes('Ollama is running')) {
            try {
                const tagsResponse = await axios.get(`http://${ip}:11434/api/tags`, {
                    timeout: 5000,
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                    }
                });
                return {
                    success: true,
                    data: {
                        isOllama: true,
                        models: tagsResponse.data.models.map(model => ({
                            name: model.name,
                            size: (model.size / 1024 / 1024 / 1024).toFixed(2) + ' GB',
                            modified_at: model.modified_at,
                            parameter_size: model.details.parameter_size,
                            family: model.details.family,
                            quantization: model.details.quantization_level
                        }))
                    }
                };
            } catch (error) {
                return {
                    success: true,
                    data: {
                        isOllama: true,
                        models: []
                    }
                };
            }
        }
        return { success: true, data: { isOllama: false } };
    } catch (error) {
        return {
            success: false,
            canRetry: retryCount < 2,
            data: { isOllama: false }
        };
    }
}

// 处理单个IP的扫描
async function handleSingleScan(ip, socket, retryCount = 0) {
    try {
        const portResult = await scanPort(ip, 11434, retryCount);
        
        if (!portResult.success) {
            if (portResult.canRetry) {
                retryQueue.push({ ip, retryCount: retryCount + 1 });
                scanStats.retried++;
                // 只在最后一次重试失败时才计入失败统计
                if (retryCount >= MAX_RETRIES - 1) {
                    scanStats.failed++;
                    scanStats.scanned++;
                }
                return;
            }
            scanStats.failed++;
            scanStats.scanned++;
            return;
        }

        const ollamaResult = await checkOllama(ip, retryCount);
        if (!ollamaResult.success) {
            if (ollamaResult.canRetry) {
                retryQueue.push({ ip, retryCount: retryCount + 1 });
                scanStats.retried++;
                // 只在最后一次重试失败时才计入失败统计
                if (retryCount >= MAX_RETRIES - 1) {
                    scanStats.failed++;
                    scanStats.scanned++;
                }
                return;
            }
            scanStats.failed++;
            scanStats.scanned++;
            return;
        }

        if (ollamaResult.data.isOllama) {
            scanStats.success++;
            const result = {
                ip: ip,
                timestamp: new Date().toISOString(),
                models: ollamaResult.data.models
            };
            saveResult(ip, result);
            socket.emit('found', result);
            
            // 如果找到模型，自动进行聊天测试
            if (result.models && result.models.length > 0) {
                // 使用Promise.all和setTimeout来创建延迟队列，避免同时发送过多请求
                const testMessages = ["Hello", "Hi there", "How are you?", "What can you do?"];
                
                // 创建一个队列来按顺序处理测试，避免过载服务器
                for (const model of result.models) {
                    // 随机选择一个测试消息
                    const randomMessage = testMessages[Math.floor(Math.random() * testMessages.length)];
                    
                    // 使用setTimeout创建延迟，不阻塞主扫描线程
                    setTimeout(async () => {
                        try {
                            console.log(`Testing chat for model ${model.name} on ${ip}`);
                            const chatResult = await testModelChat(ip, model.name, randomMessage);
                            socket.emit('model_chat_result', {
                                ip,
                                model: model.name,
                                message: randomMessage,
                                result: chatResult,
                                modelInfo: model
                            });
                            saveChatTestResult({
                                ip,
                                model: model.name,
                                message: randomMessage,
                                result: chatResult,
                                modelInfo: model
                            });
                        } catch (err) {
                            console.error(`Error testing model ${model.name}:`, err);
                            socket.emit('model_chat_result', {
                                ip,
                                model: model.name,
                                message: randomMessage,
                                result: {
                                    success: false,
                                    response: null,
                                    responseTime: null,
                                    error: err.message || 'Unknown error'
                                },
                                modelInfo: model
                            });
                            saveChatTestResult({
                                ip,
                                model: model.name,
                                message: randomMessage,
                                result: {
                                    success: false,
                                    response: null,
                                    responseTime: null,
                                    error: err.message || 'Unknown error'
                                },
                                modelInfo: model
                            });
                        }
                    }, 1000); // 每个模型测试间隔1秒，避免过载
                }
            }
        } else {
            scanStats.failed++;
        }
        scanStats.scanned++;
    } catch (error) {
        console.error(`Error scanning ${ip}:`, error);
        if (retryCount < MAX_RETRIES) {
            retryQueue.push({ ip, retryCount: retryCount + 1 });
            scanStats.retried++;
            // 只在最后一次重试失败时才计入失败统计
            if (retryCount >= MAX_RETRIES - 1) {
                scanStats.failed++;
                scanStats.scanned++;
            }
        } else {
            scanStats.failed++;
            scanStats.scanned++;
        }
    } finally {
        socket.emit('progress', { 
            currentIP: ip,
            stats: scanStats
        });
        saveProgress(ip);
    }
}

// 处理重试队列
async function processRetryQueue(socket) {
    while (retryQueue.length > 0 && isScanning) {
        const batch = retryQueue.splice(0, 100);
        await Promise.all(batch.map(item => 
            new Promise(resolve => 
                setTimeout(() => {
                    handleSingleScan(item.ip, socket, item.retryCount).then(resolve);
                }, 2000)
            )
        ));
    }
}

// 添加聊天消息测试函数
async function testModelChat(ip, modelName, message) {
    try {
        const startTime = Date.now();
        const response = await axios.post(`http://${ip}:11434/api/generate`, 
            {
                model: modelName,
                prompt: message,
                stream: false
            },
            {
                timeout: 30000,
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
                }
            }
        );
        
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        return {
            success: true,
            response: response.data.response,
            responseTime: responseTime,
            error: null
        };
    } catch (error) {
        return {
            success: false,
            response: null,
            responseTime: null,
            error: error.message || 'Unknown error'
        };
    }
}

// 扫描控制和统计
let isScanning = false;
let scanQueue = [];
let retryQueue = [];
const CONCURRENT_SCANS = 100; 
const MAX_RETRIES = 2;
const RETRY_DELAY = 2000;

// 扫描统计
let scanStats = {
    total: 0,
    scanned: 0,
    success: 0,
    failed: 0,
    retried: 0
};

// 扫描控制器
async function startScanning(socket, customIPs = null) {
    if (isScanning) return;
    isScanning = true;
    
    // 重置统计数据
    scanStats = {
        total: 0,
        scanned: 0,
        success: 0,
        failed: 0,
        retried: 0
    };
    
    let ipsToScan;
    if (customIPs) {
        ipsToScan = parseIPInput(customIPs);
    } else {
        ipsToScan = [];
        for (const range of JP_IP_RANGES) {
            const startLong = ip2long(range.start);
            const endLong = ip2long(range.end);
            for (let ipLong = startLong; ipLong <= endLong; ipLong++) {
                ipsToScan.push(long2ip(ipLong));
            }
        }
    }

    const progress = loadProgress();
    if (progress.lastScannedIP) {
        const lastIndex = ipsToScan.indexOf(progress.lastScannedIP);
        if (lastIndex !== -1) {
            ipsToScan = ipsToScan.slice(lastIndex + 1);
        }
    }

    scanStats.total = ipsToScan.length;
    socket.emit('total_ips', { total: ipsToScan.length });

    while (isScanning && (ipsToScan.length > 0 || retryQueue.length > 0)) {
        if (ipsToScan.length > 0) {
            const batch = ipsToScan.splice(0, CONCURRENT_SCANS);
            await Promise.all(batch.map(ip => handleSingleScan(ip, socket)));
        }
        
        // 处理重试队列
        if (retryQueue.length > 0) {
            await processRetryQueue(socket);
        }
    }

    if (!ipsToScan.length && !retryQueue.length) {
        socket.emit('scan_complete', { finalStats: scanStats });
    }
    isScanning = false;
}

// 停止扫描
function stopScanning() {
    isScanning = false;
}

// 静态文件服务
app.use(express.static('public'));

// WebSocket连接处理
io.on('connection', (socket) => {
    console.log('Client connected');
    
    socket.on('start_scan', (data) => {
        startScanning(socket, data?.customIPs);
    });

    socket.on('stop_scan', () => {
        stopScanning();
        socket.emit('scan_stopped');
    });
    
    // 处理聊天测试请求
    socket.on('test_model_chat', async (data) => {
        try {
            const { ip, model, message } = data;
            console.log(`Testing chat for ${model} on ${ip} with message: ${message}`);
            
            const result = await testModelChat(ip, model.name, message);
            
            socket.emit('model_chat_result', {
                ip,
                model: model.name,
                message,
                result,
                modelInfo: model
            });
            saveChatTestResult({
                ip,
                model: model.name,
                message,
                result,
                modelInfo: model
            });
        } catch (err) {
            console.error('Error testing model chat:', err);
            socket.emit('model_chat_result', {
                ip: data.ip,
                model: data.model.name,
                message: data.message,
                result: {
                    success: false,
                    response: null,
                    responseTime: null,
                    error: err.message || 'Unknown error'
                },
                modelInfo: data.model
            });
            saveChatTestResult({
                ip: data.ip,
                model: data.model.name,
                message: data.message,
                result: {
                    success: false,
                    response: null,
                    responseTime: null,
                    error: err.message || 'Unknown error'
                },
                modelInfo: data.model
            });
        }
    });
    
    socket.on('get_results', () => {
        try {
            console.log('Received request for results');
            let results = [];
            if (fs.existsSync(RESULTS_FILE)) {
                console.log(`Reading results from ${RESULTS_FILE}`);
                results = JSON.parse(fs.readFileSync(RESULTS_FILE));
                console.log(`Found ${results.length} total results`);
                
                // 处理新的数据格式
                const uniqueResults = new Map();
                results.forEach(result => {
                    // 检查是否有data字段（新格式）或直接包含models字段（旧格式）
                    const modelData = result.data || result;
                    if (modelData.models && modelData.models.length > 0) {
                        // 使用data中的信息或直接使用result
                        uniqueResults.set(result.ip, {
                            ip: result.ip,
                            timestamp: result.timestamp,
                            models: modelData.models
                        });
                    }
                });
                
                results = Array.from(uniqueResults.values());
                console.log(`After filtering, ${results.length} results with models`);
                
                // 按时间倒序排序
                results.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
            } else {
                console.log('Results file does not exist');
            }
            console.log('Sending results to client');
            socket.emit('results', results);
        } catch (err) {
            console.error('Error reading results:', err);
            socket.emit('results', []);
        }
    });

    socket.on('get_chat_test_results', () => {
        try {
            console.log('Received request for chat test results');
            if (fs.existsSync(CHAT_TEST_RESULTS_FILE)) {
                console.log(`Reading chat test results from ${CHAT_TEST_RESULTS_FILE}`);
                const results = JSON.parse(fs.readFileSync(CHAT_TEST_RESULTS_FILE));
                console.log(`Found ${results.length} chat test results`);
                socket.emit('chat_test_results', results);
            } else {
                console.log('Chat test results file does not exist');
                socket.emit('chat_test_results', []);
            }
        } catch (err) {
            console.error('Error reading chat test results:', err);
            socket.emit('chat_test_results', []);
        }
    });
});

// 启动服务器
const PORT = process.env.PORT || 3005;
server.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
    // 启动时加载聊天测试结果
    loadChatTestResults();
});
