const fs = require('fs');

// URL和IP解析函数
function parseURLAndIP(url) {
    try {
        // 如果是纯IP地址（可能带端口）
        if (/^\d+\.\d+\.\d+\.\d+(:?\d+)?$/.test(url)) {
            const [ip, port] = url.split(':');
            return { ip, port: port || '11434' };
        }
        
        // 如果是URL格式
        if (url.startsWith('http://') || url.startsWith('https://')) {
            const urlObj = new URL(url);
            let hostname = urlObj.hostname;
            let port = urlObj.port || (urlObj.protocol === 'https:' ? '443' : '80');
            
            // 如果hostname是IP地址，直接返回
            if (/^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
                return { ip: hostname, port };
            }
            
            // 如果是域名，需要DNS解析，这里先返回域名，后续处理
            return { hostname, port };
        }
        
        // 如果是域名格式（不带协议）
        if (url.includes('.') && !url.includes(':')) {
            return { hostname: url, port: '11434' };
        }
        
        // 其他格式，尝试作为IP处理
        return { ip: url, port: '11434' };
    } catch (error) {
        console.error('Error parsing URL/IP:', url, error);
        return null;
    }
}

// IP解析函数（增强版，支持URL和各种格式）
function parseIPInput(input) {
    const targets = new Set();
    const lines = input.split('\n');
    
    lines.forEach(line => {
        line = line.trim();
        if (!line || line === 'host') return; // 跳过空行和标题行
        
        if (line.includes('-')) {
            // IP范围格式: ***********-*************
            const [start, end] = line.split('-').map(ip => ip.trim());
            try {
                // 这里简化处理，实际应用中需要ip2long和long2ip函数
                console.log(`IP Range: ${start} - ${end}`);
                targets.add(JSON.stringify({ ip: start, port: '11434' }));
                targets.add(JSON.stringify({ ip: end, port: '11434' }));
            } catch (error) {
                console.error('Error parsing IP range:', line, error);
            }
        } else {
            // 单个URL/IP/域名
            const parsed = parseURLAndIP(line);
            if (parsed) {
                targets.add(JSON.stringify(parsed));
            }
        }
    });
    
    return Array.from(targets).map(target => JSON.parse(target));
}

// 测试解析函数
const testContent = fs.readFileSync('test_host.txt', 'utf8');
console.log('Testing parser with content:');
console.log(testContent);
console.log('\nParsed results:');
const results = parseIPInput(testContent);
results.forEach((result, index) => {
    console.log(`${index + 1}:`, result);
});
