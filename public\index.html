<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ollama Service Finder</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .scan-results {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
        }
        .progress-info {
            margin: 20px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .chat-test-results {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
        }
        .chat-test-item {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .response-time {
            font-weight: bold;
            color: #28a745;
        }
        .error-message {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">Ollama Service Finder(11434 port scanner)</h1>
        
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">IP Input</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="ipInput" class="form-label">Custom IP Ranges (Optional)</label>
                    <textarea class="form-control" id="ipInput" rows="4" placeholder="Enter IP ranges (e.g., ***********-*************) or individual IPs, one per line"></textarea>
                </div>
                <div class="mb-3">
                    <label for="ipFile" class="form-label">Or Import IP List File</label>
                    <input type="file" class="form-control" id="ipFile" accept=".txt">
                </div>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-12">
                <button id="startScan" class="btn btn-primary">Start Scanning</button>
                <button id="stopScan" class="btn btn-danger ms-2" disabled>Stop Scanning</button>
                <button id="loadResults" class="btn btn-secondary ms-2">Load Previous Results</button>
                <a href="/chat_test_result.html" class="btn btn-info ms-2">View Chat Test Results</a>
            </div>
        </div>

        <div class="progress-info">
            <h5>Current Progress:</h5>
            <div class="progress mb-2">
                <div class="progress-bar" id="progressBar" role="progressbar" style="width: 0%"></div>
            </div>
            <div id="currentProgress">Not scanning</div>
            <div id="scanStats" class="small text-muted"></div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Scan Results</h5>
            </div>
            <div class="card-body">
                <div class="scan-results" id="results">
                    <!-- Results will be displayed here -->
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">Chat Test Results</h5>
            </div>
            <div class="card-body">
                <div class="chat-test-results" id="chatTestResults">
                    <!-- Chat test results will be displayed here -->
                </div>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
