# Japanese IP Port Scanner

This application scans Japanese IP ranges for open port 11434 and detects Ollama services.

## Features

- Scans Japanese IP ranges for port 11434
- Detects Ollama services
- Web interface for monitoring scan progress
- Saves scan progress to avoid duplicate scans
- Stores results of found services

## Installation

1. Install Node.js if you haven't already
2. Clone this repository
3. Run `npm install` to install dependencies

## Usage

1. Start the server:
```bash
npm start
```

2. Open your browser and navigate to `http://localhost:3005`

3. Click "Start Scanning" to begin the scan
4. Use "Load Previous Results" to view previously found services

## Technical Details

- The scanner automatically saves progress to avoid rescanning the same IPs
- Results are stored in `scan_results.json`
- Progress is stored in `scan_progress.json`
- The application scans major Japanese IP ranges
- Port 11434 is checked for Ollama services
