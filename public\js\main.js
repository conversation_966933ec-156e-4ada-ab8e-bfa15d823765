const socket = io();

// 连接成功后立即加载结果
socket.on('connect', () => {
    console.log('Socket connected, loading results...');
    socket.emit('get_results');
});

// DOM Elements
const startScanBtn = document.getElementById('startScan');
const scanHostFileBtn = document.getElementById('scanHostFile');
const stopScanBtn = document.getElementById('stopScan');
const loadResultsBtn = document.getElementById('loadResults');
const progressDiv = document.getElementById('currentProgress');
const progressBar = document.getElementById('progressBar');
const scanStats = document.getElementById('scanStats');
const resultsDiv = document.getElementById('results');
const ipInput = document.getElementById('ipInput');
const ipFile = document.getElementById('ipFile');
const chatTestResultsDiv = document.getElementById('chatTestResults');

let totalIPs = 0;
let scannedIPs = 0;
let modelChatResults = new Map(); // 用于存储聊天测试结果

// 页面加载完成后也尝试加载结果
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, loading results...');
    socket.emit('get_results');
});

// File Import Handler
ipFile.addEventListener('change', (event) => {
    const file = event.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
            ipInput.value = e.target.result;
        };
        reader.readAsText(file);
    }
});

// Event Listeners
startScanBtn.addEventListener('click', () => {
    startScanBtn.disabled = true;
    stopScanBtn.disabled = false;
    scannedIPs = 0;
    progressBar.style.width = '0%';
    progressDiv.textContent = 'Starting scan...';
    // 清空聊天测试结果
    chatTestResultsDiv.innerHTML = '';
    modelChatResults.clear();
    socket.emit('start_scan', { customIPs: ipInput.value });
});

scanHostFileBtn.addEventListener('click', () => {
    startScanBtn.disabled = true;
    scanHostFileBtn.disabled = true;
    stopScanBtn.disabled = false;
    scannedIPs = 0;
    progressBar.style.width = '0%';
    progressDiv.textContent = 'Starting scan from host.txt...';
    // 清空聊天测试结果
    modelChatResults.clear();
    updateChatTestResults();

    socket.emit('scan_host_file');
});

stopScanBtn.addEventListener('click', () => {
    socket.emit('stop_scan');
});

loadResultsBtn.addEventListener('click', () => {
    socket.emit('get_results');
});

// Socket Events
socket.on('total_ips', (data) => {
    totalIPs = data.total;
    scanStats.textContent = `Total IPs to scan: ${totalIPs}`;
});

socket.on('progress', (data) => {
    if (data.stats) {
        const percentage = ((data.stats.scanned / data.stats.total) * 100).toFixed(2);
        progressBar.style.width = `${percentage}%`;
        progressDiv.textContent = `Currently scanning: ${data.currentIP}`;
        
        // 显示详细统计信息
        scanStats.textContent = `
            Scanned: ${data.stats.scanned} / ${data.stats.total} (${percentage}%)
            | Success: ${data.stats.success}
            | Failed: ${data.stats.failed}
            | Retried: ${data.stats.retried}
        `;
    }
});

// 处理聊天测试结果
socket.on('model_chat_result', (data) => {
    console.log('Received chat test result:', data);
    
    // 创建唯一ID用于存储结果
    const resultId = `${data.ip}-${data.model}`;
    modelChatResults.set(resultId, data);
    
    // 创建或更新聊天测试结果显示
    updateChatTestResultDisplay(data);
});

// 更新聊天测试结果显示
function updateChatTestResultDisplay(data) {
    const resultId = `chat-test-${data.ip.replace(/\./g, '-')}-${data.model}`;
    let resultElement = document.getElementById(resultId);
    
    if (!resultElement) {
        resultElement = document.createElement('div');
        resultElement.id = resultId;
        resultElement.className = 'chat-test-item';
        chatTestResultsDiv.insertBefore(resultElement, chatTestResultsDiv.firstChild);
    }
    
    let statusHtml = '';
    if (data.result.success) {
        statusHtml = `
            <div class="mt-2">
                <strong>Response Time:</strong> <span class="response-time">${data.result.responseTime}ms</span><br>
                <strong>Response:</strong> <div class="p-2 border rounded mt-1">${data.result.response}</div>
            </div>
        `;
    } else {
        statusHtml = `
            <div class="mt-2">
                <strong>Status:</strong> <span class="error-message">Failed</span><br>
                <strong>Error:</strong> <span class="error-message">${data.result.error}</span>
            </div>
        `;
    }
    
    // 获取模型详细信息
    const modelInfo = data.modelInfo;
    let modelInfoHtml = '';
    if (modelInfo) {
        modelInfoHtml = `
            <div class="mt-2 small">
                <strong>Size:</strong> ${modelInfo.size || 'Unknown'} | 
                <strong>Parameters:</strong> ${modelInfo.parameter_size || 'Unknown'} | 
                <strong>Family:</strong> ${modelInfo.family || 'Unknown'} | 
                <strong>Quantization:</strong> ${modelInfo.quantization || 'Unknown'}
            </div>
        `;
    }
    
    resultElement.innerHTML = `
        <div>
            <h6>Model Chat Test</h6>
            <strong>IP:</strong> ${data.ip}<br>
            <strong>Model:</strong> ${data.model}<br>
            <strong>Message:</strong> "${data.message}"
            ${statusHtml}
            ${modelInfoHtml}
        </div>
    `;
}

socket.on('found', (result) => {
    const resultElement = document.createElement('div');
    resultElement.className = 'alert alert-success';
    
    let modelsHtml = '';
    if (result.models && result.models.length > 0) {
        modelsHtml = `
        <div class="mt-2">
            <strong>Available Models:</strong>
            <table class="table table-sm table-bordered mt-2">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Size</th>
                        <th>Parameters</th>
                        <th>Family</th>
                        <th>Quantization</th>
                    </tr>
                </thead>
                <tbody>
                    ${result.models.map(model => `
                        <tr>
                            <td>${model.name}</td>
                            <td>${model.size}</td>
                            <td>${model.parameter_size}</td>
                            <td>${model.family}</td>
                            <td>${model.quantization}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>`;
    } else {
        modelsHtml = '<div class="mt-2">No models found or unable to fetch model list</div>';
    }

    const displayTarget = result.hostname ? `${result.hostname} (${result.ip})` : result.ip;
    const port = result.port || '11434';

    resultElement.innerHTML = `
        <strong>Found Ollama Service!</strong><br>
        Target: ${displayTarget}<br>
        Port: ${port}<br>
        Time: ${new Date(result.timestamp).toLocaleString()}
        ${modelsHtml}
    `;
    resultsDiv.insertBefore(resultElement, resultsDiv.firstChild);
});

socket.on('results', (results) => {
    console.log('Received results:', results);
    resultsDiv.innerHTML = '';
    if (results && results.length > 0) {
        results.forEach(result => {
            const resultElement = document.createElement('div');
            resultElement.className = 'result-item mb-3 p-3 border rounded';
            
            const timestamp = new Date(result.timestamp).toLocaleString();
            let modelsHtml = '';
            if (result.models && result.models.length > 0) {
                modelsHtml = `
                <div class="mt-2">
                    <strong>Available Models:</strong>
                    <div class="table-responsive">
                        <table class="table table-sm table-bordered mt-2">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Size</th>
                                    <th>Parameters</th>
                                    <th>Family</th>
                                    <th>Quantization</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${result.models.map(model => `
                                    <tr>
                                        <td>${model.name}</td>
                                        <td>${model.size}</td>
                                        <td>${model.parameter_size}</td>
                                        <td>${model.family}</td>
                                        <td>${model.quantization}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>`;
            }

            const displayTarget = result.hostname ? `${result.hostname} (${result.ip})` : result.ip;
            const port = result.port || '11434';

            resultElement.innerHTML = `
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <strong>Found Service:</strong><br>
                        Target: ${displayTarget}<br>
                        Port: ${port}<br>
                        Time: ${timestamp}
                    </div>
                    <button class="btn btn-sm btn-primary rescan-btn" data-ip="${result.ip}">Rescan</button>
                </div>
                ${modelsHtml}
            `;

            // 添加重新扫描按钮事件监听器
            const rescanBtn = resultElement.querySelector('.rescan-btn');
            rescanBtn.addEventListener('click', () => {
                socket.emit('scan', { ip: result.ip });
                rescanBtn.disabled = true;
                rescanBtn.textContent = 'Scanning...';
            });

            resultsDiv.appendChild(resultElement);
        });
    } else {
        resultsDiv.innerHTML = '<div class="alert alert-info">No results found</div>';
    }
});

socket.on('scan_complete', (data) => {
    startScanBtn.disabled = false;
    scanHostFileBtn.disabled = false;
    stopScanBtn.disabled = true;
    progressDiv.textContent = 'Scan completed';
    
    // 显示最终统计信息
    if (data.finalStats) {
        scanStats.textContent = `
            Final Results:
            Total Scanned: ${data.finalStats.scanned} / ${data.finalStats.total}
            | Success: ${data.finalStats.success}
            | Failed: ${data.finalStats.failed}
            | Retried: ${data.finalStats.retried}
        `;
    }
});

socket.on('scan_stopped', () => {
    startScanBtn.disabled = false;
    scanHostFileBtn.disabled = false;
    stopScanBtn.disabled = true;
    progressDiv.textContent = 'Scan stopped by user';
});
